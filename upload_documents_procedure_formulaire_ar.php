<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON><PERSON>\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

/**
 * Script d'upload uniquement des documents pour les procédures et formulaires (version arabe JSON)
 * Usage: drush php:script upload_documents_procedure_formulaire_ar.php
 */

// Chemin vers le fichier JSON
$json_file_path = '/var/www/html/mtl/output-ar.json';

// Dossier des fichiers à uploader
$files_directory = '/var/www/html/mtl/Portail MTL - Collecte du contenu';

// Vérifier si le fichier JSON existe
if (!file_exists($json_file_path)) {
  echo "ERREUR: Le fichier JSON n'existe pas: $json_file_path\n";
  return;
}

// Vérifier si le dossier des fichiers existe
if (!is_dir($files_directory)) {
  echo "ERREUR: Le dossier des fichiers n'existe pas: $files_directory\n";
  return;
}

echo "=== DÉBUT DE L'UPLOAD DES DOCUMENTS PROCÉDURES/FORMULAIRES (JSON ARABE) ===\n";
echo "Fichier JSON: $json_file_path\n";
echo "Dossier des fichiers: $files_directory\n\n";

// Statistiques globales
$stats = [
  'total_items' => 0,
  'nodes_found' => 0,
  'nodes_not_found' => 0,
  'files_found' => 0,
  'files_not_found' => 0,
  'files_uploaded' => 0,
  'files_existing' => 0,
  'files_errors' => 0,
  'nodes_updated' => 0,
];

/**
 * Fonction pour trouver un fichier à partir du chemin du JSON
 */
function findFileFromJSONPath($base_directory, $json_path) {
  if (empty($json_path)) {
    return null;
  }
  
  echo "  → Chemin JSON: $json_path\n";
  
  // Essayer d'abord le chemin complet tel que dans le JSON
  $full_path = $base_directory . '/' . $json_path;
  echo "  → Tentative chemin complet: $full_path\n";
  
  if (file_exists($full_path)) {
    echo "  ✓ Fichier trouvé par chemin complet: $full_path\n";
    return $full_path;
  }
  
  // Si le chemin complet ne marche pas, extraire juste le nom du fichier et chercher récursivement
  $filename = basename($json_path);
  echo "  → Recherche récursive du fichier: $filename\n";
  
  $iterator = new RecursiveIteratorIterator(
    new RecursiveDirectoryIterator($base_directory, RecursiveDirectoryIterator::SKIP_DOTS),
    RecursiveIteratorIterator::LEAVES_ONLY
  );
  
  foreach ($iterator as $file) {
    if ($file->isFile() && $file->getFilename() === $filename) {
      echo "  ✓ Fichier trouvé par recherche récursive: " . $file->getPathname() . "\n";
      return $file->getPathname();
    }
  }
  
  echo "  ✗ Fichier non trouvé: $json_path\n";
  return null;
}

/**
 * Fonction pour uploader un fichier dans Drupal
 */
function uploadFileToDrupal($file_path, $files_directory) {
  global $stats;
  
  if (empty($file_path)) {
    return null;
  }
  
  echo "  === UPLOAD ===\n";
  echo "  Chemin du fichier: $file_path\n";
  
  // Nettoyer le chemin du fichier
  $file_path = trim($file_path);
  
  // Chercher le fichier à partir du chemin JSON
  $actual_file_path = findFileFromJSONPath($files_directory, $file_path);
  
  if (!$actual_file_path || !file_exists($actual_file_path)) {
    echo "  ✗ Fichier non accessible: $file_path\n";
    $stats["files_not_found"]++;
    return null;
  }
  
  $stats["files_found"]++;
  
  try {
    // Vérifier si le fichier existe déjà dans Drupal par nom
    $existing_files = \Drupal::entityTypeManager()
      ->getStorage('file')
      ->loadByProperties(['filename' => basename($actual_file_path)]);
    
    if ($existing_files) {
      $existing_file = reset($existing_files);
      echo "  ⚡ Fichier existant trouvé dans Drupal: " . $existing_file->label() . " (ID: " . $existing_file->id() . ")\n";
      $stats["files_existing"]++;
      return $existing_file;
    }
    
    // Préparer le répertoire de destination
    $destination_dir = 'public://procedure-formulaire/json-ar/';
    \Drupal::service('file_system')->prepareDirectory($destination_dir, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
    
    echo "  → Lecture du fichier: $actual_file_path\n";
    $file_data = file_get_contents($actual_file_path);
    
    if ($file_data === FALSE) {
      echo "  ✗ Erreur lors de la lecture du fichier: $actual_file_path\n";
      $stats["files_errors"]++;
      return null;
    }
    
    echo "  → Taille du fichier: " . strlen($file_data) . " bytes\n";
    
    // Copier le fichier vers Drupal
    $destination = $destination_dir . basename($actual_file_path);
    echo "  → Destination: $destination\n";
    
    // Créer l'entité fichier
    $file = \Drupal::service('file.repository')->writeData($file_data, $destination, FileSystemInterface::EXISTS_RENAME);
    
    if ($file) {
      $file->setPermanent();
      $file->save();
      echo "  ✓ Fichier uploadé avec succès: " . $file->label() . " (ID: " . $file->id() . ")\n";
      echo "  → URI: " . $file->getFileUri() . "\n";
      $stats["files_uploaded"]++;
      return $file;
    } else {
      echo "  ✗ Erreur lors de la création du fichier dans Drupal\n";
      $stats["files_errors"]++;
      return null;
    }
    
  } catch (Exception $e) {
    echo "  ✗ Exception lors de l'upload: " . $e->getMessage() . "\n";
    $stats["files_errors"]++;
    return null;
  }
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title) {
  echo "  → Recherche du nœud procedure_formulaire: $title\n";
  
  // Recherche par titre
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'procedure_formulaire')
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    $node = Node::load(reset($nids));
    echo "  ✓ Nœud trouvé par titre: ID " . $node->id() . "\n";
    return $node;
  }
  
  echo "  ✗ Nœud non trouvé\n";
  return null;
}

// Lire le fichier JSON complet
$json_content = file_get_contents($json_file_path);
if (!$json_content) {
  echo "ERREUR: Impossible de lire le fichier JSON\n";
  return;
}

// Décoder le JSON
$json_data = json_decode($json_content, true);
if (json_last_error() !== JSON_ERROR_NONE) {
  echo "ERREUR: Fichier JSON invalide: " . json_last_error_msg() . "\n";
  return;
}

if (!is_array($json_data)) {
  echo "ERREUR: Le JSON ne contient pas un tableau d'éléments\n";
  return;
}

echo "Total d'éléments dans le JSON: " . count($json_data) . "\n\n";

// Traiter chaque élément du JSON
foreach ($json_data as $index => $item) {
  $item_number = $index + 1;
  $stats['total_items']++;
  
  echo "=== ÉLÉMENT $item_number ===\n";
  
  if (!isset($item['titre']) || empty($item['titre'])) {
    echo "ÉLÉMENT $item_number: Ignoré (titre manquant)\n\n";
    continue;
  }
  
  try {
    $titre = trim($item['titre']);
    $image = isset($item['image']) ? trim($item['image']) : '';
    
    echo "Titre: $titre\n";
    echo "Image: $image\n";
    
    // Chercher le nœud existant
    $existing_node = getExistingNode($titre);
    
    if (!$existing_node) {
      echo "✗ Nœud non trouvé - IGNORÉ\n\n";
      $stats['nodes_not_found']++;
      continue;
    }
    
    $stats['nodes_found']++;
    echo "✓ Nœud trouvé: ID " . $existing_node->id() . "\n";
    
    // Vérifier les fichiers actuels du nœud
    if ($existing_node->hasField('field_lien_telechargement')) {
      $current_files = $existing_node->get('field_lien_telechargement')->getValue();
      echo "Fichiers actuels dans le nœud: " . count($current_files) . "\n";
      
      foreach ($current_files as $file_info) {
        if (isset($file_info['target_id'])) {
          $file = File::load($file_info['target_id']);
          if ($file) {
            echo "  - " . $file->label() . " (ID: " . $file->id() . ")\n";
          }
        }
      }
    }
    
    // Uploader les fichiers si l'image est spécifiée
    $files_to_add = [];
    
    if (!empty($image)) {
      $file = uploadFileToDrupal($image, $files_directory);
      if ($file) {
        $files_to_add[] = [
          'target_id' => $file->id(),
          'description' => 'Document associé',
        ];
      }
    }
    
    // Mettre à jour le nœud avec les nouveaux fichiers
    if (!empty($files_to_add) && $existing_node->hasField('field_lien_telechargement')) {
      $current_files = $existing_node->get('field_lien_telechargement')->getValue();
      $existing_file_ids = array_column($current_files, 'target_id');
      
      $new_files = [];
      foreach ($files_to_add as $file_info) {
        if (!in_array($file_info['target_id'], $existing_file_ids)) {
          $new_files[] = $file_info;
        }
      }
      
      if (!empty($new_files)) {
        $all_files = array_merge($current_files, $new_files);
        $existing_node->set('field_lien_telechargement', $all_files);
        $existing_node->save();
        
        echo "✓ Nœud mis à jour avec " . count($new_files) . " nouveau(x) fichier(s)\n";
        echo "Total fichiers dans le nœud: " . count($all_files) . "\n";
        $stats['nodes_updated']++;
      } else {
        echo "⚡ Tous les fichiers étaient déjà présents dans le nœud\n";
      }
    } else if (empty($files_to_add)) {
      echo "✗ Aucun fichier à ajouter\n";
    }
    
  } catch (Exception $e) {
    echo "ERREUR élément $item_number: " . $e->getMessage() . "\n";
  }
  
  echo "\n";
}

// Afficher le résumé détaillé
echo "\n";
echo "==================================================\n";
echo "            RÉSUMÉ DÉTAILLÉ FINAL\n";
echo "==================================================\n";
echo "Total éléments traités: " . $stats['total_items'] . "\n";
echo "\n--- NŒUDS ---\n";
echo "Nœuds trouvés: " . $stats['nodes_found'] . "\n";
echo "Nœuds non trouvés: " . $stats['nodes_not_found'] . "\n";
echo "Nœuds mis à jour: " . $stats['nodes_updated'] . "\n";
echo "\n--- FICHIERS ---\n";
echo "Fichiers trouvés sur disque: " . $stats['files_found'] . "\n";
echo "Fichiers non trouvés: " . $stats['files_not_found'] . "\n";
echo "Fichiers uploadés (nouveaux): " . $stats['files_uploaded'] . "\n";
echo "Fichiers existants (réutilisés): " . $stats['files_existing'] . "\n";
echo "Erreurs upload: " . $stats['files_errors'] . "\n";
echo "\n--- TOTAUX ---\n";
echo "Total fichiers trouvés: " . $stats['files_found'] . "\n";
echo "Total fichiers uploadés: " . $stats['files_uploaded'] . "\n";
echo "Total fichiers existants: " . $stats['files_existing'] . "\n";
echo "Total erreurs: " . $stats['files_errors'] . "\n";
echo "==================================================\n";
echo "Upload terminé!\n";